# 小镜微服务 Docker Compose 部署指南

本项目将原有的多个 Docker run 命令转换为 Docker Compose 配置，便于统一管理和部署。

## 文件说明

- `docker-compose.yml`: Docker Compose 主配置文件
- `.env`: 环境变量配置文件，包含所有可配置的路径和端口
- `README.md`: 本说明文件

## 服务列表

本配置包含以下微服务：

### API 服务
- **订单服务** (xiaojing-order): 端口 8181
- **网关服务** (xiaojing-gateway): 端口 8001  
- **设备服务** (xiaojing-device): 端口 8801
- **车辆服务** (xiaojing-vehicle): 端口 8281
- **支付服务** (xiaojing-pay): 端口 8681
- **用户服务** (xiaojing-user): 端口 8081
- **发票服务** (xiaojing-invoice): 端口 8381
- **员工服务** (xiaojing-staffapi): 端口 8781
- **营销服务** (xiaojing-markerapi): 端口 8092

### RPC 服务
- **Core RPC** (xiaojing-corerpc): 端口 9201
- **Core API** (xiaojing-coreapi): 端口 9200
- **FMS RPC** (xiaojing-fmsrpc): 端口 9202
- **Job API** (xiaojing-jobapi): 端口 9203

### 消息队列服务
- **订单消息队列** (xiaojing-ordermq): 无外部端口
- **支付消息队列** (xiaojing-paymq): 无外部端口
- **营销消息队列** (xiaojing-marketmq): 无外部端口

### IoT 网关服务
- **IoT Pub 网关** (iot-gateways-pub): 端口 8091
- **IoT TCP 网关** (iot-gateways-tcp): 端口 9000
- **IoT 消费者** (iot-gateways-consumers): 无外部端口

### 前端服务
- **后台前端** (xiaojing-backend-front): 端口 9003 (Nginx)

## 使用方法

### 1. 准备工作

确保已安装 Docker 和 Docker Compose：
```bash
docker --version
docker-compose --version
```

### 2. 创建网络

首先创建项目网络（如果尚未存在）：
```bash
docker network create project_network
```

### 3. 配置环境变量

编辑 `.env` 文件，根据实际部署环境修改路径和端口配置：
```bash
# 例如修改订单服务端口
ORDER_EXTERNAL_PORT=8181

# 例如修改订单服务文件路径
ORDER_API_PATH=/your/custom/path/order/orderapi
```

### 4. 启动服务

启动所有服务：
```bash
docker-compose up -d
```

启动特定服务：
```bash
docker-compose up -d xiaojing-order xiaojing-gateway
```

### 5. 查看服务状态

```bash
# 查看所有服务状态
docker-compose ps

# 查看服务日志
docker-compose logs xiaojing-order

# 实时查看日志
docker-compose logs -f xiaojing-order
```

### 6. 停止服务

```bash
# 停止所有服务
docker-compose down

# 停止特定服务
docker-compose stop xiaojing-order
```

## 配置说明

### 端口配置
所有外部端口都可以通过 `.env` 文件中的环境变量进行配置，格式为：
```
服务名_EXTERNAL_PORT=端口号
```

### 路径配置
所有挂载路径都可以通过 `.env` 文件中的环境变量进行配置，格式为：
```
服务名_路径类型_PATH=实际路径
```

### 网络配置
所有服务都连接到 `project_network` 网络，该网络需要预先创建。

## 注意事项

1. 确保所有配置的路径在宿主机上存在
2. 确保配置的端口没有被其他服务占用
3. 部分服务可能有依赖关系，建议按顺序启动
4. 生产环境部署时，建议先在测试环境验证配置

## 故障排除

### 常见问题

1. **端口冲突**: 检查 `.env` 文件中的端口配置
2. **路径不存在**: 确保 `.env` 文件中配置的路径在宿主机上存在
3. **网络问题**: 确保 `project_network` 网络已创建
4. **权限问题**: 确保 Docker 有权限访问配置的路径

### 查看详细日志
```bash
docker-compose logs --details 服务名
```
