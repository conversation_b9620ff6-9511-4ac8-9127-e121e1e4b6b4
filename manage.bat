@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

:: 小镜微服务管理脚本 (Windows版本)

:: 颜色定义 (Windows 10/11)
set "RED=[91m"
set "GREEN=[92m"
set "YELLOW=[93m"
set "BLUE=[94m"
set "NC=[0m"

:: 打印带颜色的消息
:print_message
echo %~1%~2%NC%
goto :eof

:: 检查Docker和Docker Compose是否安装
:check_requirements
call :print_message "%BLUE%" "检查系统要求..."

docker --version >nul 2>&1
if errorlevel 1 (
    call :print_message "%RED%" "错误: Docker 未安装"
    exit /b 1
)

docker-compose --version >nul 2>&1
if errorlevel 1 (
    call :print_message "%RED%" "错误: Docker Compose 未安装"
    exit /b 1
)

call :print_message "%GREEN%" "✓ Docker 和 Docker Compose 已安装"
goto :eof

:: 创建项目网络
:create_network
call :print_message "%BLUE%" "创建项目网络..."

docker network ls | findstr "project_network" >nul
if not errorlevel 1 (
    call :print_message "%YELLOW%" "⚠ 网络 project_network 已存在"
) else (
    docker network create project_network
    call :print_message "%GREEN%" "✓ 网络 project_network 创建成功"
)
goto :eof

:: 启动所有服务
:start_all
call :print_message "%BLUE%" "启动所有服务..."
call :check_requirements
if errorlevel 1 exit /b 1
call :create_network
docker-compose up -d
call :print_message "%GREEN%" "✓ 所有服务启动完成"
goto :eof

:: 停止所有服务
:stop_all
call :print_message "%BLUE%" "停止所有服务..."
docker-compose down
call :print_message "%GREEN%" "✓ 所有服务已停止"
goto :eof

:: 重启所有服务
:restart_all
call :print_message "%BLUE%" "重启所有服务..."
call :stop_all
call :start_all
goto :eof

:: 查看服务状态
:status
call :print_message "%BLUE%" "查看服务状态..."
docker-compose ps
goto :eof

:: 查看服务日志
:logs
if "%~1"=="" (
    call :print_message "%BLUE%" "查看所有服务日志..."
    docker-compose logs
) else (
    call :print_message "%BLUE%" "查看服务 %~1 的日志..."
    docker-compose logs %~1
)
goto :eof

:: 实时查看服务日志
:logs_follow
if "%~1"=="" (
    call :print_message "%BLUE%" "实时查看所有服务日志..."
    docker-compose logs -f
) else (
    call :print_message "%BLUE%" "实时查看服务 %~1 的日志..."
    docker-compose logs -f %~1
)
goto :eof

:: 启动特定服务
:start_service
if "%~1"=="" (
    call :print_message "%RED%" "错误: 请指定服务名称"
    exit /b 1
)

call :print_message "%BLUE%" "启动服务: %~1"
call :check_requirements
if errorlevel 1 exit /b 1
call :create_network
docker-compose up -d %~1
call :print_message "%GREEN%" "✓ 服务 %~1 启动完成"
goto :eof

:: 停止特定服务
:stop_service
if "%~1"=="" (
    call :print_message "%RED%" "错误: 请指定服务名称"
    exit /b 1
)

call :print_message "%BLUE%" "停止服务: %~1"
docker-compose stop %~1
call :print_message "%GREEN%" "✓ 服务 %~1 已停止"
goto :eof

:: 重启特定服务
:restart_service
if "%~1"=="" (
    call :print_message "%RED%" "错误: 请指定服务名称"
    exit /b 1
)

call :print_message "%BLUE%" "重启服务: %~1"
docker-compose restart %~1
call :print_message "%GREEN%" "✓ 服务 %~1 重启完成"
goto :eof

:: 显示帮助信息
:show_help
echo 小镜微服务管理脚本
echo.
echo 用法: %~nx0 [命令] [参数]
echo.
echo 命令:
echo   start                 启动所有服务
echo   stop                  停止所有服务
echo   restart               重启所有服务
echo   status                查看服务状态
echo   logs [服务名]         查看服务日志
echo   logs-follow [服务名]  实时查看服务日志
echo   start-service 服务名  启动特定服务
echo   stop-service 服务名   停止特定服务
echo   restart-service 服务名 重启特定服务
echo   help                  显示此帮助信息
echo.
echo 服务名列表:
echo   xiaojing-order        订单服务
echo   xiaojing-gateway      网关服务
echo   xiaojing-device       设备服务
echo   xiaojing-vehicle      车辆服务
echo   xiaojing-pay          支付服务
echo   xiaojing-user         用户服务
echo   xiaojing-invoice      发票服务
echo   xiaojing-staffapi     员工服务
echo   xiaojing-markerapi    营销服务
echo   xiaojing-corerpc      Core RPC服务
echo   xiaojing-coreapi      Core API服务
echo   xiaojing-fmsrpc       FMS RPC服务
echo   xiaojing-jobapi       Job API服务
echo   xiaojing-ordermq      订单消息队列
echo   xiaojing-paymq        支付消息队列
echo   xiaojing-marketmq     营销消息队列
echo   iot-gateways-pub      IoT Pub网关
echo   iot-gateways-tcp      IoT TCP网关
echo   iot-gateways-consumers IoT消费者
echo   xiaojing-backend-front 后台前端
echo.
echo 示例:
echo   %~nx0 start                      # 启动所有服务
echo   %~nx0 start-service xiaojing-order # 启动订单服务
echo   %~nx0 logs xiaojing-order        # 查看订单服务日志
echo   %~nx0 status                     # 查看所有服务状态
goto :eof

:: 主函数
:main
if "%~1"=="" (
    call :show_help
    exit /b 0
)

if "%~1"=="start" (
    call :start_all
) else if "%~1"=="stop" (
    call :stop_all
) else if "%~1"=="restart" (
    call :restart_all
) else if "%~1"=="status" (
    call :status
) else if "%~1"=="logs" (
    call :logs %~2
) else if "%~1"=="logs-follow" (
    call :logs_follow %~2
) else if "%~1"=="start-service" (
    call :start_service %~2
) else if "%~1"=="stop-service" (
    call :stop_service %~2
) else if "%~1"=="restart-service" (
    call :restart_service %~2
) else if "%~1"=="help" (
    call :show_help
) else if "%~1"=="--help" (
    call :show_help
) else if "%~1"=="-h" (
    call :show_help
) else (
    call :print_message "%RED%" "错误: 未知命令 '%~1'"
    echo.
    call :show_help
    exit /b 1
)
goto :eof

:: 执行主函数
call :main %*
