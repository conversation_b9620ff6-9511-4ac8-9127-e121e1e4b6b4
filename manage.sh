#!/bin/bash

# 小镜微服务管理脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_message() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

# 检查Docker和Docker Compose是否安装
check_requirements() {
    print_message $BLUE "检查系统要求..."

    if ! command -v docker &> /dev/null; then
        print_message $RED "错误: Docker 未安装"
        exit 1
    fi

    if ! docker compose version &> /dev/null; then
        print_message $RED "错误: Docker Compose 未安装"
        exit 1
    fi

    print_message $GREEN "✓ Docker 和 Docker Compose 已安装"
}

# 创建项目网络
create_network() {
    print_message $BLUE "创建项目网络..."

    if docker network ls | grep -q "project_network"; then
        print_message $YELLOW "⚠ 网络 project_network 已存在"
    else
        docker network create project_network
        print_message $GREEN "✓ 网络 project_network 创建成功"
    fi
}

# 启动所有服务
start_all() {
    print_message $BLUE "启动所有服务..."
    check_requirements
    create_network
    docker-compose up -d
    print_message $GREEN "✓ 所有服务启动完成"
}

# 停止所有服务
stop_all() {
    print_message $BLUE "停止所有服务..."
    docker-compose down
    print_message $GREEN "✓ 所有服务已停止"
}

# 重启所有服务
restart_all() {
    print_message $BLUE "重启所有服务..."
    stop_all
    start_all
}

# 查看服务状态
status() {
    print_message $BLUE "查看服务状态..."
    docker-compose ps
}

# 查看服务日志
logs() {
    local service=$1
    if [ -z "$service" ]; then
        print_message $BLUE "查看所有服务日志..."
        docker-compose logs
    else
        print_message $BLUE "查看服务 $service 的日志..."
        docker-compose logs "$service"
    fi
}

# 实时查看服务日志
logs_follow() {
    local service=$1
    if [ -z "$service" ]; then
        print_message $BLUE "实时查看所有服务日志..."
        docker-compose logs -f
    else
        print_message $BLUE "实时查看服务 $service 的日志..."
        docker-compose logs -f "$service"
    fi
}

# 启动特定服务
start_service() {
    local service=$1
    if [ -z "$service" ]; then
        print_message $RED "错误: 请指定服务名称"
        exit 1
    fi

    print_message $BLUE "启动服务: $service"
    check_requirements
    create_network
    docker-compose up -d "$service"
    print_message $GREEN "✓ 服务 $service 启动完成"
}

# 停止特定服务
stop_service() {
    local service=$1
    if [ -z "$service" ]; then
        print_message $RED "错误: 请指定服务名称"
        exit 1
    fi

    print_message $BLUE "停止服务: $service"
    docker-compose stop "$service"
    print_message $GREEN "✓ 服务 $service 已停止"
}

# 重启特定服务
restart_service() {
    local service=$1
    if [ -z "$service" ]; then
        print_message $RED "错误: 请指定服务名称"
        exit 1
    fi

    print_message $BLUE "重启服务: $service"
    docker-compose restart "$service"
    print_message $GREEN "✓ 服务 $service 重启完成"
}

# 显示帮助信息
show_help() {
    echo "小镜微服务管理脚本"
    echo ""
    echo "用法: $0 [命令] [参数]"
    echo ""
    echo "命令:"
    echo "  start                 启动所有服务"
    echo "  stop                  停止所有服务"
    echo "  restart               重启所有服务"
    echo "  status                查看服务状态"
    echo "  logs [服务名]         查看服务日志"
    echo "  logs-follow [服务名]  实时查看服务日志"
    echo "  start-service 服务名  启动特定服务"
    echo "  stop-service 服务名   停止特定服务"
    echo "  restart-service 服务名 重启特定服务"
    echo "  help                  显示此帮助信息"
    echo ""
    echo "服务名列表:"
    echo "  xiaojing-order        订单服务"
    echo "  xiaojing-gateway      网关服务"
    echo "  xiaojing-device       设备服务"
    echo "  xiaojing-vehicle      车辆服务"
    echo "  xiaojing-pay          支付服务"
    echo "  xiaojing-user         用户服务"
    echo "  xiaojing-invoice      发票服务"
    echo "  xiaojing-staffapi     员工服务"
    echo "  xiaojing-markerapi    营销服务"
    echo "  xiaojing-corerpc      Core RPC服务"
    echo "  xiaojing-coreapi      Core API服务"
    echo "  xiaojing-fmsrpc       FMS RPC服务"
    echo "  xiaojing-jobapi       Job API服务"
    echo "  xiaojing-ordermq      订单消息队列"
    echo "  xiaojing-paymq        支付消息队列"
    echo "  xiaojing-marketmq     营销消息队列"
    echo "  iot-gateways-pub      IoT Pub网关"
    echo "  iot-gateways-tcp      IoT TCP网关"
    echo "  iot-gateways-consumers IoT消费者"
    echo "  xiaojing-backend-front 后台前端"
    echo ""
    echo "示例:"
    echo "  $0 start                      # 启动所有服务"
    echo "  $0 start-service xiaojing-order # 启动订单服务"
    echo "  $0 logs xiaojing-order        # 查看订单服务日志"
    echo "  $0 status                     # 查看所有服务状态"
}

# 主函数
main() {
    case "$1" in
        start)
            start_all
            ;;
        stop)
            stop_all
            ;;
        restart)
            restart_all
            ;;
        status)
            status
            ;;
        logs)
            logs "$2"
            ;;
        logs-follow)
            logs_follow "$2"
            ;;
        start-service)
            start_service "$2"
            ;;
        stop-service)
            stop_service "$2"
            ;;
        restart-service)
            restart_service "$2"
            ;;
        help|--help|-h)
            show_help
            ;;
        *)
            print_message $RED "错误: 未知命令 '$1'"
            echo ""
            show_help
            exit 1
            ;;
    esac
}

# 如果没有参数，显示帮助
if [ $# -eq 0 ]; then
    show_help
    exit 0
fi

# 执行主函数
main "$@"
