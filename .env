# 小镜微服务Docker Compose环境变量配置文件
# 请根据实际部署环境修改以下配置

# ==================== 订单服务配置 ====================
ORDER_EXTERNAL_PORT=8181
ORDER_API_PATH=/xiaojing_opt/order/orderapi
ORDER_ETC_PATH=/xiaojing_opt/order/etc
ORDER_LOGS_PATH=/xiaojing_opt/order/logs

# ==================== 网关服务配置 ====================
GATEWAY_EXTERNAL_PORT=8001
GATEWAY_API_PATH=/xiaojing_opt/gateway/gateway
GATEWAY_ETC_PATH=/xiaojing_opt/gateway/etc
GATEWAY_LOGS_PATH=/xiaojing_opt/gateway/logs

# ==================== 设备服务配置 ====================
DEVICE_EXTERNAL_PORT=8801
DEVICE_API_PATH=/xiaojing_opt/device/deviceapi
DEVICE_ETC_PATH=/xiaojing_opt/device/etc
DEVICE_LOGS_PATH=/xiaojing_opt/device/logs

# ==================== 车辆服务配置 ====================
VEHICLE_EXTERNAL_PORT=8281
VEHICLE_API_PATH=/xiaojing_opt/vehicle/vehicleapi
VEHICLE_ETC_PATH=/xiaojing_opt/vehicle/etc
VEHICLE_LOGS_PATH=/xiaojing_opt/vehicle/logs

# ==================== 支付服务配置 ====================
PAY_EXTERNAL_PORT=8681
PAY_API_PATH=/xiaojing_opt/pay/payapi
PAY_ETC_PATH=/xiaojing_opt/pay/etc
PAY_LOGS_PATH=/xiaojing_opt/pay/logs

# ==================== 用户服务配置 ====================
USER_EXTERNAL_PORT=8081
USER_API_PATH=/xiaojing_opt/user/userapi
USER_ETC_PATH=/xiaojing_opt/user/etc
USER_LOGS_PATH=/xiaojing_opt/user/logs

# ==================== 发票服务配置 ====================
INVOICE_EXTERNAL_PORT=8381
INVOICE_API_PATH=/xiaojing_opt/invoice/invoiceapi
INVOICE_ETC_PATH=/xiaojing_opt/invoice/etc
INVOICE_LOGS_PATH=/xiaojing_opt/invoice/logs

# ==================== Core RPC服务配置 ====================
CORERPC_EXTERNAL_PORT=9201
CORERPC_API_PATH=/xiaojing_opt/corerpc/corerpc
CORERPC_ETC_PATH=/xiaojing_opt/corerpc/etc

# ==================== Core API服务配置 ====================
COREAPI_EXTERNAL_PORT=9200
COREAPI_API_PATH=/xiaojing_opt/coreapi/coreapi
COREAPI_ETC_PATH=/xiaojing_opt/coreapi/etc

# ==================== FMS RPC服务配置 ====================
FMSRPC_EXTERNAL_PORT=9202
FMSRPC_API_PATH=/xiaojing_opt/fmsrpc/fmsrpc
FMSRPC_ETC_PATH=/xiaojing_opt/fmsrpc/etc

# ==================== Job API服务配置 ====================
JOBAPI_EXTERNAL_PORT=9203
JOBAPI_API_PATH=/xiaojing_opt/jobapi/jobapi
JOBAPI_ETC_PATH=/xiaojing_opt/jobapi/etc

# ==================== Staff API服务配置 ====================
STAFFAPI_EXTERNAL_PORT=8781
STAFFAPI_API_PATH=/xiaojing_opt/staff/staffapi
STAFFAPI_ETC_PATH=/xiaojing_opt/staff/etc
STAFFAPI_LOGS_PATH=/xiaojing_opt/staff/logs

# ==================== Order MQ服务配置 ====================
ORDERMQ_API_PATH=/xiaojing_opt/ordermq/ordermq
ORDERMQ_ETC_PATH=/xiaojing_opt/ordermq/etc
ORDERMQ_LOGS_PATH=/xiaojing_opt/ordermq/logs

# ==================== Pay MQ服务配置 ====================
PAYMQ_API_PATH=/xiaojing_opt/paymq/paymq
PAYMQ_ETC_PATH=/xiaojing_opt/paymq/etc
PAYMQ_LOGS_PATH=/xiaojing_opt/paymq/logs

# ==================== Marker API服务配置 ====================
MARKERAPI_EXTERNAL_PORT=8092
MARKERAPI_API_PATH=/xiaojing_opt/marker/marker_service
MARKERAPI_ETC_PATH=/xiaojing_opt/marker/etc
MARKERAPI_LOGS_PATH=/xiaojing_opt/marker/logs

# ==================== Market MQ服务配置 ====================
MARKETMQ_API_PATH=/xiaojing_opt/marketmq/marketmq
MARKETMQ_ETC_PATH=/xiaojing_opt/marketmq/etc
MARKETMQ_LOGS_PATH=/xiaojing_opt/marketmq/logs

# ==================== IoT Gateway Pub服务配置 ====================
IOT_PUB_EXTERNAL_PORT=8091
IOT_PUB_PATH=/xiaojing_opt/iot_gateways/pub/pub

# ==================== IoT Gateway TCP服务配置 ====================
IOT_TCP_EXTERNAL_PORT=9000
IOT_TCP_SERVER_PATH=/xiaojing_opt/iot_gateways/tcp/tcp_server
IOT_TCP_CONF_PATH=/xiaojing_opt/iot_gateways/tcp/conf
IOT_TCP_LOGS_PATH=/xiaojing_opt/iot_gateways/tcp/logs

# ==================== IoT Gateway Consumers服务配置 ====================
IOT_CONSUMERS_PATH=/xiaojing_opt/iot_gateways/consumers/consumers
IOT_CONSUMERS_CONFIG_PATH=/xiaojing_opt/iot_gateways/consumers/config.yaml
IOT_CONSUMERS_LOGS_PATH=/xiaojing_opt/iot_gateways/consumers/logs

# ==================== 前端Nginx服务配置 ====================
NGINX_EXTERNAL_PORT=9003
NGINX_CONF_D_PATH=/home/<USER>/fronted/nginx/etc/nginx/conf.d
NGINX_LOG_PATH=/home/<USER>/fronted/nginx/var/log/nginx
NGINX_MAIN_CONF_PATH=/home/<USER>/fronted/nginx/etc/nginx/nginx.conf
NGINX_FRONTEND_PATH=/home/<USER>/fronted/nginx/usr/share/nginx/backend_front
