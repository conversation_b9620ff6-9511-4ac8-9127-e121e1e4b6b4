version: '3.8'

services:
  # 订单服务
  xiaojing-order:
    image: crpi-tti8mr2e0jib8enl.cn-shanghai.personal.cr.aliyuncs.com/xudianxia/orderapi:v1.0.1
    container_name: xiaojing_order
    restart: unless-stopped
    networks:
      - project_network
    ports:
      - "${ORDER_EXTERNAL_PORT:-8181}:8180"
    volumes:
      - "${ORDER_API_PATH:-/xiaojing_opt/order/orderapi}:/app/orderapi"
      - "${ORDER_ETC_PATH:-/xiaojing_opt/order/etc}:/app/etc"
      - "${ORDER_LOGS_PATH:-/xiaojing_opt/order/logs}:/app/logs"
    stdin_open: true
    tty: true

  # 网关服务
  xiaojing-gateway:
    image: crpi-tti8mr2e0jib8enl.cn-shanghai.personal.cr.aliyuncs.com/xudianxia/gateway:v1.0.1
    container_name: xiaojing_gateway
    restart: unless-stopped
    networks:
      - project_network
    ports:
      - "${GATEWAY_EXTERNAL_PORT:-8001}:8000"
    volumes:
      - "${GATEWAY_API_PATH:-/xiaojing_opt/gateway/gateway}:/app/gateway"
      - "${GATEWAY_ETC_PATH:-/xiaojing_opt/gateway/etc}:/app/etc"
      - "${GATEWAY_LOGS_PATH:-/xiaojing_opt/gateway/logs}:/app/logs"
    stdin_open: true
    tty: true

  # 设备服务
  xiaojing-device:
    image: crpi-tti8mr2e0jib8enl.cn-shanghai.personal.cr.aliyuncs.com/xudianxia/deviceapi:v1.0.1
    container_name: xiaojing_device
    restart: unless-stopped
    networks:
      - project_network
    ports:
      - "${DEVICE_EXTERNAL_PORT:-8801}:8800"
    volumes:
      - "${DEVICE_API_PATH:-/xiaojing_opt/device/deviceapi}:/app/deviceapi"
      - "${DEVICE_ETC_PATH:-/xiaojing_opt/device/etc}:/app/etc"
      - "${DEVICE_LOGS_PATH:-/xiaojing_opt/device/logs}:/app/logs"
    stdin_open: true
    tty: true

  # 车辆服务
  xiaojing-vehicle:
    image: crpi-tti8mr2e0jib8enl.cn-shanghai.personal.cr.aliyuncs.com/xudianxia/vehcileapi:v1.0.1
    container_name: xiaojing_vehicle
    restart: unless-stopped
    networks:
      - project_network
    ports:
      - "${VEHICLE_EXTERNAL_PORT:-8281}:8280"
    volumes:
      - "${VEHICLE_API_PATH:-/xiaojing_opt/vehicle/vehicleapi}:/app/vehicleapi"
      - "${VEHICLE_ETC_PATH:-/xiaojing_opt/vehicle/etc}:/app/etc"
      - "${VEHICLE_LOGS_PATH:-/xiaojing_opt/vehicle/logs}:/app/logs"
    stdin_open: true
    tty: true

  # 支付服务
  xiaojing-pay:
    image: crpi-tti8mr2e0jib8enl.cn-shanghai.personal.cr.aliyuncs.com/xudianxia/payapi:v1.0.2
    container_name: xiaojing_pay
    restart: unless-stopped
    networks:
      - project_network
    ports:
      - "${PAY_EXTERNAL_PORT:-8681}:8680"
    volumes:
      - "${PAY_API_PATH:-/xiaojing_opt/pay/payapi}:/app/payapi"
      - "${PAY_ETC_PATH:-/xiaojing_opt/pay/etc}:/app/etc"
      - "${PAY_LOGS_PATH:-/xiaojing_opt/pay/logs}:/app/logs"
    stdin_open: true
    tty: true

  # 用户服务
  xiaojing-user:
    image: crpi-tti8mr2e0jib8enl.cn-shanghai.personal.cr.aliyuncs.com/xudianxia/userapi:v1.0.2
    container_name: xiaojing_user
    restart: unless-stopped
    networks:
      - project_network
    ports:
      - "${USER_EXTERNAL_PORT:-8081}:8080"
    volumes:
      - "${USER_API_PATH:-/xiaojing_opt/user/userapi}:/app/userapi"
      - "${USER_ETC_PATH:-/xiaojing_opt/user/etc}:/app/etc"
      - "${USER_LOGS_PATH:-/xiaojing_opt/user/logs}:/app/logs"
    stdin_open: true
    tty: true

  # 发票服务
  xiaojing-invoice:
    image: crpi-tti8mr2e0jib8enl.cn-shanghai.personal.cr.aliyuncs.com/xudianxia/invoiceapi:v1.0.1
    container_name: xiaojing_invoice
    restart: unless-stopped
    networks:
      - project_network
    ports:
      - "${INVOICE_EXTERNAL_PORT:-8381}:8380"
    volumes:
      - "${INVOICE_API_PATH:-/xiaojing_opt/invoice/invoiceapi}:/app/invoiceapi"
      - "${INVOICE_ETC_PATH:-/xiaojing_opt/invoice/etc}:/app/etc"
      - "${INVOICE_LOGS_PATH:-/xiaojing_opt/invoice/logs}:/app/logs"
    stdin_open: true
    tty: true

  # Core RPC服务
  xiaojing-corerpc:
    image: crpi-tti8mr2e0jib8enl.cn-shanghai.personal.cr.aliyuncs.com/xudianxia/corerpc:v1.0.0
    container_name: xiaojing_corerpc
    restart: unless-stopped
    networks:
      - project_network
    ports:
      - "${CORERPC_EXTERNAL_PORT:-9201}:9101"
    volumes:
      - "${CORERPC_API_PATH:-/xiaojing_opt/corerpc/corerpc}:/app/corerpc"
      - "${CORERPC_ETC_PATH:-/xiaojing_opt/corerpc/etc}:/app/etc"
    stdin_open: true
    tty: true

  # Core API服务
  xiaojing-coreapi:
    image: crpi-tti8mr2e0jib8enl.cn-shanghai.personal.cr.aliyuncs.com/xudianxia/coreapi:v1.0.0
    container_name: xiaojing_coreapi
    restart: unless-stopped
    networks:
      - project_network
    ports:
      - "${COREAPI_EXTERNAL_PORT:-9200}:9100"
    volumes:
      - "${COREAPI_API_PATH:-/xiaojing_opt/coreapi/coreapi}:/app/coreapi"
      - "${COREAPI_ETC_PATH:-/xiaojing_opt/coreapi/etc}:/app/etc"
    stdin_open: true
    tty: true

  # FMS RPC服务
  xiaojing-fmsrpc:
    image: crpi-tti8mr2e0jib8enl.cn-shanghai.personal.cr.aliyuncs.com/xudianxia/fmsrpc:v1.0.1
    container_name: xiaojing_fmsrpc
    restart: unless-stopped
    networks:
      - project_network
    ports:
      - "${FMSRPC_EXTERNAL_PORT:-9202}:9102"
    volumes:
      - "${FMSRPC_API_PATH:-/xiaojing_opt/fmsrpc/fmsrpc}:/app/fmsrpc"
      - "${FMSRPC_ETC_PATH:-/xiaojing_opt/fmsrpc/etc}:/app/etc"
    stdin_open: true
    tty: true

  # Job API服务
  xiaojing-jobapi:
    image: crpi-tti8mr2e0jib8enl.cn-shanghai.personal.cr.aliyuncs.com/xudianxia/jobapi:v1.0.1
    container_name: xiaojing_jobapi
    restart: unless-stopped
    networks:
      - project_network
    ports:
      - "${JOBAPI_EXTERNAL_PORT:-9203}:9103"
    volumes:
      - "${JOBAPI_API_PATH:-/xiaojing_opt/jobapi/jobapi}:/app/jobapi"
      - "${JOBAPI_ETC_PATH:-/xiaojing_opt/jobapi/etc}:/app/etc"
    stdin_open: true
    tty: true

  # Staff API服务
  xiaojing-staffapi:
    image: crpi-tti8mr2e0jib8enl.cn-shanghai.personal.cr.aliyuncs.com/xudianxia/staffapi:v1.0.0
    container_name: xiaojing_staffapi
    restart: unless-stopped
    networks:
      - project_network
    ports:
      - "${STAFFAPI_EXTERNAL_PORT:-8781}:8780"
    volumes:
      - "${STAFFAPI_API_PATH:-/xiaojing_opt/staff/staffapi}:/app/staffapi"
      - "${STAFFAPI_ETC_PATH:-/xiaojing_opt/staff/etc}:/app/etc"
      - "${STAFFAPI_LOGS_PATH:-/xiaojing_opt/staff/logs}:/app/logs"
    stdin_open: true
    tty: true

  # Order MQ服务
  xiaojing-ordermq:
    image: crpi-tti8mr2e0jib8enl.cn-shanghai.personal.cr.aliyuncs.com/xudianxia/orderrmq:V1.0.1
    container_name: xiaojing_ordermq
    restart: unless-stopped
    networks:
      - project_network
    volumes:
      - "${ORDERMQ_API_PATH:-/xiaojing_opt/ordermq/ordermq}:/app/ordermq"
      - "${ORDERMQ_ETC_PATH:-/xiaojing_opt/ordermq/etc}:/app/etc"
      - "${ORDERMQ_LOGS_PATH:-/xiaojing_opt/ordermq/logs}:/app/logs"
    stdin_open: true
    tty: true

  # Pay MQ服务
  xiaojing-paymq:
    image: crpi-tti8mr2e0jib8enl.cn-shanghai.personal.cr.aliyuncs.com/xudianxia/paymq:v1.0.0
    container_name: xiaojing_paymq
    restart: unless-stopped
    networks:
      - project_network
    volumes:
      - "${PAYMQ_API_PATH:-/xiaojing_opt/paymq/paymq}:/app/paymq"
      - "${PAYMQ_ETC_PATH:-/xiaojing_opt/paymq/etc}:/app/etc"
      - "${PAYMQ_LOGS_PATH:-/xiaojing_opt/paymq/logs}:/app/logs"
    stdin_open: true
    tty: true

  # Marker API服务
  xiaojing-markerapi:
    image: crpi-tti8mr2e0jib8enl.cn-shanghai.personal.cr.aliyuncs.com/xudianxia/markerapi:V1.0.1
    container_name: xiaojing_markerapi
    restart: unless-stopped
    networks:
      - project_network
    ports:
      - "${MARKERAPI_EXTERNAL_PORT:-8092}:8091"
    volumes:
      - "${MARKERAPI_API_PATH:-/xiaojing_opt/marker/marker_service}:/app/marker_service"
      - "${MARKERAPI_ETC_PATH:-/xiaojing_opt/marker/etc}:/app/etc"
      - "${MARKERAPI_LOGS_PATH:-/xiaojing_opt/marker/logs}:/app/logs"
    stdin_open: true
    tty: true

  # Market MQ服务
  xiaojing-marketmq:
    image: crpi-tti8mr2e0jib8enl.cn-shanghai.personal.cr.aliyuncs.com/xudianxia/marketmq:V1.0.1
    container_name: xiaojing_marketmq
    restart: unless-stopped
    networks:
      - project_network
    volumes:
      - "${MARKETMQ_API_PATH:-/xiaojing_opt/marketmq/marketmq}:/app/marketmq"
      - "${MARKETMQ_ETC_PATH:-/xiaojing_opt/marketmq/etc}:/app/etc"
      - "${MARKETMQ_LOGS_PATH:-/xiaojing_opt/marketmq/logs}:/app/logs"
    stdin_open: true
    tty: true

  # IoT Gateway Pub服务
  iot-gateways-pub:
    image: crpi-tti8mr2e0jib8enl.cn-shanghai.personal.cr.aliyuncs.com/xudianxia/iot_gateway_pub:v2
    container_name: iot_gateways_pub_xiaojing
    restart: unless-stopped
    networks:
      - project_network
    ports:
      - "${IOT_PUB_EXTERNAL_PORT:-8091}:8090"
    volumes:
      - "${IOT_PUB_PATH:-/xiaojing_opt/iot_gateways/pub/pub}:/app/pub"
    stdin_open: true
    tty: true

  # IoT Gateway TCP服务
  iot-gateways-tcp:
    image: crpi-tti8mr2e0jib8enl.cn-shanghai.personal.cr.aliyuncs.com/xudianxia/iot_gateway_tcp:v2
    container_name: iot_gateways_tcp_xiaojing
    restart: unless-stopped
    networks:
      - project_network
    ports:
      - "${IOT_TCP_EXTERNAL_PORT:-9000}:9000"
    volumes:
      - "${IOT_TCP_SERVER_PATH:-/xiaojing_opt/iot_gateways/tcp/tcp_server}:/app/tcp_server"
      - "${IOT_TCP_CONF_PATH:-/xiaojing_opt/iot_gateways/tcp/conf}:/app/conf"
      - "${IOT_TCP_LOGS_PATH:-/xiaojing_opt/iot_gateways/tcp/logs}:/app/logs"
    stdin_open: true
    tty: true

  # IoT Gateway Consumers服务
  iot-gateways-consumers:
    image: crpi-tti8mr2e0jib8enl.cn-shanghai.personal.cr.aliyuncs.com/xudianxia/iot_gateway_consumers:v2
    container_name: iot_gateways_consumers_xiaojing
    restart: unless-stopped
    networks:
      - project_network
    volumes:
      - "${IOT_CONSUMERS_PATH:-/xiaojing_opt/iot_gateways/consumers/consumers}:/app/consumers"
      - "${IOT_CONSUMERS_CONFIG_PATH:-/xiaojing_opt/iot_gateways/consumers/config.yaml}:/app/config.yaml"
      - "${IOT_CONSUMERS_LOGS_PATH:-/xiaojing_opt/iot_gateways/consumers/logs}:/app/logs"
    stdin_open: true
    tty: true

  # 前端Nginx服务
  xiaojing-backend-front:
    image: nginx:1.25.2
    container_name: xiaojing_backend_front
    restart: always
    privileged: true
    networks:
      - project_network
    ports:
      - "${NGINX_EXTERNAL_PORT:-9003}:80"
    environment:
      - TZ=Asia/Shanghai
    volumes:
      - "${NGINX_CONF_D_PATH:-/home/<USER>/fronted/nginx/etc/nginx/conf.d}:/etc/nginx/conf.d"
      - "${NGINX_LOG_PATH:-/home/<USER>/fronted/nginx/var/log/nginx}:/var/log/nginx"
      - "${NGINX_MAIN_CONF_PATH:-/home/<USER>/fronted/nginx/etc/nginx/nginx.conf}:/etc/nginx/nginx.conf"
      - "${NGINX_FRONTEND_PATH:-/home/<USER>/fronted/nginx/usr/share/nginx/backend_front}:/usr/share/nginx/backend_front"

networks:
  project_network:
    external: true
